import { useMessageGetter } from '@messageformat/react';
import type {
  PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema,
  PotentialChangeListSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { getApiProjectsProjectIdControlCenterPotentialChanges } from '@shape-construction/api/src/api';
import {
  getApiProjectsProjectIdControlCenterPotentialChangesSuspenseQueryOptions,
  useGetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId,
  usePatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId,
  usePostApiProjectsProjectIdControlCenterPotentialChanges,
  usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive,
  usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport,
} from '@shape-construction/api/src/hooks';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import type { InfiniteData } from '@tanstack/react-query';
import { infiniteQueryOptions, useQueryClient } from '@tanstack/react-query';
import type { Draft } from 'immer';
import { produce } from 'immer';
import queryClient from '../query.client.builder';

type CacheType = PotentialChangeListSchema & InfiniteData<PotentialChangeListSchema, unknown>;
type MutationContext = { previousData?: unknown };

const updateCacheWithRecord = (
  cache: CacheType | undefined,
  record: PotentialChangeSchema,
  findFn: (r: PotentialChangeSchema) => boolean
) => {
  if (!cache) return undefined;
  return produce(cache, (draft: Draft<CacheType>) => {
    for (const page of draft.pages) {
      const recordIndex = page.entries.findIndex(findFn);
      if (recordIndex !== -1) {
        page.entries[recordIndex] = record;
        break;
      }
    }
  });
};

const setupMutation = async (queryClient: any, projectId: string) => {
  await queryClient.cancelQueries({
    queryKey: getApiProjectsProjectIdControlCenterPotentialChangesSuspenseQueryOptions(projectId).queryKey,
  });
  return queryClient.getQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey);
};

const handleError = (messages: any, errorKey: string, projectId: string, previousData: unknown, queryClient: any) => {
  showErrorToast({
    message: messages(errorKey),
    alignContent: 'start',
  });
  if (previousData) {
    queryClient.setQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey, previousData as any);
  }
};

const handleSuccess = (
  messages: any,
  successKey: string,
  updatedRecord: PotentialChangeSchema,
  projectId: string,
  queryClient: any
) => {
  showSuccessToast({
    message: messages(successKey),
    alignContent: 'start',
  });
  queryClient.setQueryData(
    getPotentialChangesInfiniteQueryOptions(projectId).queryKey,
    (cache: CacheType | undefined) => updateCacheWithRecord(cache, updatedRecord, (r) => r.id === updatedRecord.id)
  );
};

export const getPotentialChangesInfiniteQueryOptions = (
  ...args: Parameters<typeof getApiProjectsProjectIdControlCenterPotentialChanges>
) => {
  const [projectId, params] = args;
  return infiniteQueryOptions({
    queryKey: getApiProjectsProjectIdControlCenterPotentialChangesSuspenseQueryOptions(projectId, params).queryKey,
    queryFn: ({ pageParam }) => {
      return getApiProjectsProjectIdControlCenterPotentialChanges(projectId, {
        ...params,
        after: pageParam || undefined,
      });
    },
    getNextPageParam: (lastPage) => {
      const { hasNextPage, lastEntryCursor } = lastPage.meta;
      return hasNextPage ? lastEntryCursor : undefined;
    },
    initialPageParam: '',
    select: (cache) => {
      return cache.pages.flatMap((page) => page.entries.filter((entry) => !entry.archived));
    },
    staleTime: Number.POSITIVE_INFINITY,
  });
};

export const DEFAULT_VALUES: PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema = {
  title: 'Untitled',
  priority: undefined,
  status: 'unprocessed',
  category: undefined,
  early_warning_notice_submitted: false,
  estimated_cost_impact: undefined,
  estimated_schedule_impact: undefined,
  comment: '',
};

export const useCreatePotentialChange = () => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  return usePostApiProjectsProjectIdControlCenterPotentialChanges({
    mutation: {
      onMutate: ({ projectId }) => {
        const timestamp = Date.now();
        const tempId = `temp-${timestamp}`;

        const tempRecord: PotentialChangeSchema = {
          id: tempId,
          archived: false,
          category: null,
          signals: [],
          signalsCount: 0,
          comment: DEFAULT_VALUES.comment || '',
          createdAt: new Date().toISOString(),
          earlyWarningNoticeSubmitted: DEFAULT_VALUES.early_warning_notice_submitted || false,
          estimatedCostImpact: null,
          estimatedScheduleImpact: null,
          priority: DEFAULT_VALUES.priority || null,
          status: DEFAULT_VALUES.status as PotentialChangeSchema['status'],
          teamMemberId: 0,
          title: DEFAULT_VALUES.title || '',
          availableActions: {
            archive: false,
            linkChangeSignals: false,
            unlinkChangeSignals: false,
          },
        };

        queryClient.setQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey, (cache) => {
          if (!cache) return undefined;
          return produce(cache, (draft) => {
            draft.pages[0].entries.unshift(tempRecord);
            draft.pages[0].meta.total += 1;
          });
        });

        return { tempId };
      },
      onSuccess: (newRecord, { projectId }, context) => {
        showSuccessToast({
          message: messages('alerts.potentialChangeCreationSuccess'),
          alignContent: 'start',
        });
        const { tempId } = context as { tempId: string };
        queryClient.setQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey, (cache) =>
          updateCacheWithRecord(cache, newRecord, (r) => r.id === tempId)
        );
      },
      onError: (_, { projectId }, context) => {
        const typedContext = context as MutationContext;
        handleError(
          messages,
          'alerts.potentialChangeCreationFailed',
          projectId,
          typedContext.previousData,
          queryClient
        );
      },
    },
  });
};

export const useArchivePotentialChange = () => {
  const queryClient = useQueryClient();
  const messages = useMessageGetter('controlCenter.commercialTracker');

  return usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive({
    mutation: {
      onMutate: async ({ projectId, potentialChangeId }) => {
        const previousData = await setupMutation(queryClient, projectId);

        queryClient.setQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey, (cache) => {
          if (!cache) return undefined;
          return produce(cache, (draft) => {
            for (const page of draft.pages) {
              const recordIndex = page.entries.findIndex((record) => record.id === potentialChangeId);
              if (recordIndex === -1) continue;
              page.entries[recordIndex].archived = true;
              break;
            }
          });
        });
        return { previousData };
      },
      onError: (_, { projectId }, context) => {
        const typedContext = context as MutationContext;
        handleError(messages, 'alerts.archiveFailed', projectId, typedContext.previousData, queryClient);
      },
      onSuccess: (updatedRecord, { projectId }) => {
        handleSuccess(messages, 'alerts.archiveSuccess', updatedRecord, projectId, queryClient);
      },
    },
  });
};

export const useUpdatePotentialChange = () => {
  const queryClient = useQueryClient();
  const messages = useMessageGetter('controlCenter.commercialTracker');

  return usePatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId({
    mutation: {
      onMutate: async ({ projectId, potentialChangeId, data }) => {
        const previousData = await setupMutation(queryClient, projectId);

        queryClient.setQueryData(getPotentialChangesInfiniteQueryOptions(projectId).queryKey, (cache) => {
          if (!cache || !data) return cache;

          return produce(cache, (draft) => {
            for (const page of draft.pages) {
              const recordIndex = page.entries.findIndex((record) => record.id === potentialChangeId);
              if (recordIndex === -1) continue;

              const record = page.entries[recordIndex];
              const fieldMap: Record<string, keyof typeof record> = {
                title: 'title',
                priority: 'priority',
                status: 'status',
                category: 'category',
                early_warning_notice_submitted: 'earlyWarningNoticeSubmitted',
                estimated_cost_impact: 'estimatedCostImpact',
                estimated_schedule_impact: 'estimatedScheduleImpact',
                comment: 'comment',
              };

              for (const [key, value] of Object.entries(data)) {
                const mappedKey = fieldMap[key];
                if (mappedKey && value !== undefined) {
                  (record as any)[mappedKey] = value;
                }
              }

              break;
            }
          });
        });

        return { previousData };
      },
      onError: (_, { projectId }, context) => {
        const typedContext = context as MutationContext;
        handleError(messages, 'alerts.fieldUpdateFailed', projectId, typedContext.previousData, queryClient);
      },
      onSuccess: (updatedRecord, { projectId }) => {
        handleSuccess(messages, 'alerts.fieldUpdateSuccess', updatedRecord, projectId, queryClient);
      },
    },
  });
};

export const useExportChange = usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport;

export const useGetPotentialChangeDetails = useGetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId;
