import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { Avatar } from '@shape-construction/arch-ui';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectPerson } from 'app/queries/projects/people';
import React from 'react';
type AuthorProps = {
  record: PotentialChangeSchema;
};

export const Author: React.FC<AuthorProps> = ({ record }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const project = useCurrentProject();
  const { data: author } = useProjectPerson(project.id, record.teamMemberId);
  return (
    <div className="flex gap-2 items-center" aria-label={messages('author')}>
      <Avatar text={author?.user.firstName?.slice(0, 2) || ''} size={'xs'} imgURL={author?.user.avatarUrl} />
      <span className="text-xs leading-4 font-normal text-neutral-subtle">{author?.user.name}</span>
    </div>
  );
};
