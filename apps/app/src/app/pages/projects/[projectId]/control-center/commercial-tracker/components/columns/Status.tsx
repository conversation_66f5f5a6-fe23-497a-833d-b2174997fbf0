import { useMessageGetter } from '@messageformat/react';
import {
  type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  type PotentialChangeSchema,
  type PotentialChangeStatusEnumSchema,
  potentialChangeStatusEnum,
} from '@shape-construction/api/src';
import { Badge, Select } from '@shape-construction/arch-ui';
import { useFieldOptions } from '../../hooks/useFieldOptions';

type StatusProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Status: React.FC<StatusProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const { getStatusTheme, getStatusLabel } = useFieldOptions();

  return (
    <Select.Root
      onChange={(value: PotentialChangeStatusEnumSchema) => {
        if (value !== record.status) {
          onUpdatePotentialChangeRecord({ status: value });
        }
      }}
      value={record.status}
      className="border-none"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('status')}
        variant="plain"
        showChevronOnHover
      >
        <Badge theme={getStatusTheme(record.status)} label={getStatusLabel(record.status)} />
      </Select.Trigger>
      <Select.ResponsivePanel className="border-none">
        <Select.Options>
          {Object.values(potentialChangeStatusEnum).map((status) => (
            <Select.Option key={status} value={status}>
              <Select.OptionText className="text-xs">
                <Badge theme={getStatusTheme(status)} label={getStatusLabel(status)} />
              </Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
