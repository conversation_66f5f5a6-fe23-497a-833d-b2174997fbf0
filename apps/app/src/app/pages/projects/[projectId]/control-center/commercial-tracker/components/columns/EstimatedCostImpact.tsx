import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeEstimatedCostImpactEnumSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { potentialChangeEstimatedCostImpactEnum } from '@shape-construction/api/src';
import { Select } from '@shape-construction/arch-ui';
import { useFieldOptions } from '../../hooks/useFieldOptions';

type EstimatedCostImpactProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const EstimatedCostImpact: React.FC<EstimatedCostImpactProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { getEstimatedCostImpactLabel } = useFieldOptions();

  return (
    <Select.Root
      onChange={(value: PotentialChangeEstimatedCostImpactEnumSchema) => {
        if (value !== record.estimatedCostImpact) {
          onUpdatePotentialChangeRecord({ estimated_cost_impact: value });
        }
      }}
      value={record.estimatedCostImpact}
      className="border-none w-40"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.estimatedCostImpact')}
        variant="plain"
        showChevronOnHover
      >
        {!record.estimatedCostImpact ? (
          <span className="text-xs leading-4 font-normal text-neutral-subtlest">{messages('select')}</span>
        ) : (
          <span className="text-xs leading-4 font-normal text-neutral-bold">
            {getEstimatedCostImpactLabel(record.estimatedCostImpact)}
          </span>
        )}
      </Select.Trigger>
      <Select.ResponsivePanel className="border-none">
        <Select.Options>
          {Object.values(potentialChangeEstimatedCostImpactEnum).map((costImpact) => (
            <Select.Option key={costImpact} value={costImpact}>
              <Select.OptionText className="text-xs">{getEstimatedCostImpactLabel(costImpact)}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
