import type { PotentialChangeSchema } from '@shape-construction/api/src';

/**
 * Checks if a potential change can be archived
 * @param potentialChange The potential change to check
 * @returns boolean indicating if the potential change can be archived
 */
export const canArchivePotentialChange = (potentialChange: PotentialChangeSchema): boolean => {
  return potentialChange.availableActions.archive;
};

/**
 * Checks if change signals can be linked to a potential change
 * @param potentialChange The potential change to check
 * @returns boolean indicating if change signals can be linked
 */
export const canLinkChangeSignals = (potentialChange: PotentialChangeSchema): boolean => {
  return potentialChange.availableActions.linkChangeSignals;
};

/**
 * Checks if change signals can be unlinked from a potential change
 * @param potentialChange The potential change to check
 * @returns boolean indicating if change signals can be unlinked
 */
export const canUnlinkChangeSignals = (potentialChange: PotentialChangeSchema): boolean => {
  return potentialChange.availableActions.unlinkChangeSignals;
};
