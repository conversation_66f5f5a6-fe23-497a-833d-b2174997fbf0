import type { ChangeSignalSchema, PotentialChangeSchema } from '@shape-construction/api/src';
import React from 'react';
import { ChangeSignalFilters } from '../../change-signals/components/ChangeSignalFilters';
import { ChangeSignalsTable } from '../../change-signals/components/ChangeSignalsTable';
import { SelectedChangeSignalsTable } from '../../change-signals/components/SelectedChangeSignalsTable';

export type PotentialChangeSignalsLinkingProps = {
  potentialChange: PotentialChangeSchema;
  selectedChangeSignals: ChangeSignalSchema[];
  setSelectedChangeSignals: React.Dispatch<React.SetStateAction<ChangeSignalSchema[]>>;
  changeSignals: ChangeSignalSchema[];
  onLinkChangeSignals: () => Promise<void>;
  issuesCount: number;
  downtimeCount: number;
  selectedSignalType: ChangeSignalSchema['signalType'];
  setSelectedSignalType: (type: ChangeSignalSchema['signalType']) => void;
  onLoadMore: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
};

export const PotentialChangeSignalsLinking: React.FC<PotentialChangeSignalsLinkingProps> = ({
  potentialChange,
  selectedChangeSignals,
  setSelectedChangeSignals,
  onLinkChangeSignals,
  changeSignals,
  issuesCount,
  downtimeCount,
  selectedSignalType,
  setSelectedSignalType,
  onLoadMore,
  hasNextPage,
  isFetchingNextPage,
}) => {
  function onRemoveSignals(signalIds: ChangeSignalSchema['signalId'][]) {
    setSelectedChangeSignals((prev) => prev.filter(({ signalId }) => !signalIds.includes(signalId)));
  }

  return (
    <div className="flex flex-col h-full">
      <SelectedChangeSignalsTable
        changeSignals={selectedChangeSignals}
        onRemoveSignals={onRemoveSignals}
        onLinkSelection={onLinkChangeSignals}
      />
      <ChangeSignalFilters
        selectedType={selectedSignalType}
        onSignalTypeChange={setSelectedSignalType}
        issuesCount={issuesCount}
        downtimeCount={downtimeCount}
      />
      <ChangeSignalsTable
        changeSignals={changeSignals}
        disabledChangeSignals={potentialChange.changeSignals}
        selectedSignals={selectedChangeSignals}
        onSelectionChange={setSelectedChangeSignals}
        onLoadMore={onLoadMore}
        hasNextPage={hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
      />
    </div>
  );
};
