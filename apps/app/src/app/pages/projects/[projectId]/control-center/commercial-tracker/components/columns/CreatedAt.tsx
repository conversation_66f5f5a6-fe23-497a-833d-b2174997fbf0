import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeSchema, ProjectSchema } from '@shape-construction/api/src';
import { formatDate } from '@shape-construction/utils/DateTime';
import React from 'react';

type CreatedAtProps = {
  record: PotentialChangeSchema;
  project: ProjectSchema;
};
export const CreatedAt: React.FC<CreatedAtProps> = ({ record, project }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  return (
    <div aria-label={messages('createdAt')}>
      <span className="text-xs leading-4 font-normal text-neutral-subtle">
        {formatDate(record.createdAt, project.timezone, 'DD-MMM-YYYY')}
      </span>
    </div>
  );
};
