import { useMessageGetter } from '@messageformat/react';
import { IconButton } from '@shape-construction/arch-ui';
import { InboxArrowDownIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useModal } from '@shape-construction/hooks';
import { useCurrentProject } from 'app/contexts/currentProject';
import React from 'react';

import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { useArchivePotentialChange } from 'app/queries/control-center/commercial-tracker';
import { canArchivePotentialChange } from '../../utils/potentialChangeActions';
import { ArchiveChangeConfirmationModal } from '../ArchiveChangeConfirmationModal';

type ArchiveProps = {
  potentialChangeId?: PotentialChangeSchema['id'];
  record: PotentialChangeSchema;
};

export const Archive: React.FC<ArchiveProps> = ({ record, potentialChangeId }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { open, closeModal, openModal } = useModal(false);
  const project = useCurrentProject();

  const { mutate: archivePotentialChange, isPending: isArchivePending } = useArchivePotentialChange();

  const onArchive = () => {
    closeModal();
    archivePotentialChange({
      projectId: project.id,
      potentialChangeId: potentialChangeId!,
    });
  };

  const canArchive = canArchivePotentialChange(record);

  return (
    <>
      {canArchive && (
        <IconButton
          aria-label={messages('fields.archive')}
          color="secondary"
          size="xxs"
          variant="text"
          icon={InboxArrowDownIcon}
          onClick={openModal}
          disabled={isArchivePending}
        />
      )}

      <ArchiveChangeConfirmationModal isOpen={open} closeModal={closeModal} onArchive={onArchive} />
    </>
  );
};
