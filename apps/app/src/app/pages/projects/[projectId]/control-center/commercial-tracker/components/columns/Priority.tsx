import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangePriorityEnumSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { potentialChangePriorityEnum } from '@shape-construction/api/src';
import { Select } from '@shape-construction/arch-ui';
import { useFieldOptions } from '../../hooks/useFieldOptions';

type PriorityProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Priority: React.FC<PriorityProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { getPriorityIcon, getPriorityLabel } = useFieldOptions();

  return (
    <Select.Root
      onChange={(value: PotentialChangePriorityEnumSchema) => {
        if (value !== record.priority) {
          onUpdatePotentialChangeRecord({ priority: value });
        }
      }}
      value={record.priority}
      className="border-none w-40"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.priority')}
        variant="plain"
        showChevronOnHover
        startAdornment={record.priority ? getPriorityIcon(record.priority) : undefined}
      >
        {!record.priority ? (
          <span className="text-xs leading-4 font-normal text-neutral-subtlest">{messages('select')}</span>
        ) : (
          <span className="text-xs leading-4 font-normal text-neutral-bold">{getPriorityLabel(record.priority)}</span>
        )}
      </Select.Trigger>
      <Select.ResponsivePanel className="border-none">
        <Select.Options>
          {Object.values(potentialChangePriorityEnum).map((priority) => (
            <Select.Option key={priority} value={priority}>
              <Select.OptionText className="text-xs">{getPriorityLabel(priority)}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
