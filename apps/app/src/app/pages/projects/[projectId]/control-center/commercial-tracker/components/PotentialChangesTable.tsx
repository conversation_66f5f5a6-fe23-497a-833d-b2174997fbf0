import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { Table, cn } from '@shape-construction/arch-ui';
import { InfiniteLoadWaypoints } from 'app/components/InfiniteLoadWaypoints/InfiniteLoadWaypoints';
import { useCurrentProject } from 'app/contexts/currentProject';
import { PotentialChangeRow } from './PotentialChangeRow';

type PotentialChangesTableProps = {
  potentialChanges: Array<PotentialChangeSchema>;
  onViewChangeSignals: (potentialChange: any) => void;
  titleRef?: React.RefObject<HTMLInputElement>;
  isSelectionMode?: boolean;
  selectedPotentialChangeIds: Array<PotentialChangeSchema['id']> | undefined;
  setSelectedPotentialChangeIds: React.Dispatch<React.SetStateAction<Array<PotentialChangeSchema['id']> | undefined>>;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  newRecordId?: PotentialChangeSchema['id'];
};

const TableHeader: React.FC<React.ComponentProps<'th'>> = ({ children, className }) => (
  <Table.Header className={cn('normal-case border-[0.5px] border-t-0 py-2 first:sm:pl-8 bg-neutral-subtle', className)}>
    <span className="truncate">{children}</span>
  </Table.Header>
);

export function PotentialChangesTable({
  potentialChanges,
  onViewChangeSignals,
  titleRef,
  isSelectionMode,
  selectedPotentialChangeIds,
  setSelectedPotentialChangeIds,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
  newRecordId,
}: PotentialChangesTableProps) {
  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter');

  return (
    <div className="grow overflow-auto">
      <Table className="border-0 table-fixed border-separate border-spacing-0" rowDensity="condensed">
        <Table.Heading className="sticky top-0 z-10">
          <Table.Row className="bg-neutral-subtle">
            {isSelectionMode && <TableHeader />}
            <TableHeader className="min-w-[215px] max-w-[215px]">
              {messages('commercialTracker.fields.title')}
            </TableHeader>
            <TableHeader>{messages('commercialTracker.fields.author')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.createdAt')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.priority')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.category')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.status')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.linkedSignals')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.estimatedCostImpact')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.estimatedScheduleImpact')}</TableHeader>
            <TableHeader>{messages('commercialTracker.fields.earlyWarningNoticeSubmitted')}</TableHeader>
            <TableHeader className="min-w-56">{messages('commercialTracker.fields.comments')}</TableHeader>
            <TableHeader className="sticky right-0 border-l-4 bg-inherit pr-4 sm:last:pr-8">
              {messages('commercialTracker.fields.actions')}
            </TableHeader>
          </Table.Row>
        </Table.Heading>
        <Table.Body>
          {potentialChanges.map((record) => (
            <PotentialChangeRow
              isSelectionMode={isSelectionMode}
              potentialChangesToLink={selectedPotentialChangeIds}
              setPotentialChangesToLink={setSelectedPotentialChangeIds}
              key={record.id}
              record={record}
              projectId={project.id}
              titleRef={titleRef}
              onViewChangeSignals={onViewChangeSignals}
              newRecordId={newRecordId}
            />
          ))}
        </Table.Body>
      </Table>
      <InfiniteLoadWaypoints hasNextPage={hasNextPage} fetchNext={fetchNextPage} isLast={true}>
        {isFetchingNextPage && (
          <div className="flex w-full items-center justify-center h-12">
            <span className="text-neutral-subtle">{messages('loading')}</span>
          </div>
        )}
      </InfiniteLoadWaypoints>
    </div>
  );
}
