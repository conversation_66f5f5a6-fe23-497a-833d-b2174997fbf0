import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { InputText } from '@shape-construction/arch-ui';
import React from 'react';

type TitleProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
  inputRef?: React.RefObject<HTMLInputElement>;
};

export const Title: React.FC<TitleProps> = ({ record, onUpdatePotentialChangeRecord, inputRef, ...props }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const [title, setTitle] = React.useState(record.title || '');

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const trimmedValue = e.target.value.trim();
    if (trimmedValue !== record.title) {
      onUpdatePotentialChangeRecord({ title: trimmedValue });
    }
  };

  return (
    <InputText
      {...props}
      aria-label={messages('title')}
      ref={inputRef}
      name="title"
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
      value={title}
      className="bg-transparent !text-xs leading-4 font-normal text-neutral-bold border-none shadow-none
        truncate px-2.5 py-1 rounded-sm
        focus:ring-2 hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
      onBlur={handleBlur}
    />
  );
};
