import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeEstimatedScheduleImpactEnumSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { potentialChangeEstimatedScheduleImpactEnum } from '@shape-construction/api/src';
import { Select } from '@shape-construction/arch-ui';
import { useFieldOptions } from '../../hooks/useFieldOptions';

type EstimatedScheduleImpactProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const EstimatedScheduleImpact: React.FC<EstimatedScheduleImpactProps> = ({
  record,
  onUpdatePotentialChangeRecord,
}) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { getEstimatedScheduleImpactLabel } = useFieldOptions();

  return (
    <Select.Root
      onChange={(value: PotentialChangeEstimatedScheduleImpactEnumSchema) => {
        if (value !== record.estimatedScheduleImpact) {
          onUpdatePotentialChangeRecord({ estimated_schedule_impact: value });
        }
      }}
      value={record.estimatedScheduleImpact}
      className="border-none w-40"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.estimatedScheduleImpact')}
        variant="plain"
        showChevronOnHover
      >
        {!record.estimatedScheduleImpact ? (
          <span className="text-xs leading-4 font-normal text-neutral-subtlest">{messages('select')}</span>
        ) : (
          <span className="text-xs leading-4 font-normal text-neutral-bold">
            {getEstimatedScheduleImpactLabel(record.estimatedScheduleImpact)}
          </span>
        )}
      </Select.Trigger>

      <Select.ResponsivePanel className="border-none">
        <Select.Options>
          {Object.values(potentialChangeEstimatedScheduleImpactEnum).map((option) => (
            <Select.Option key={option} value={option}>
              <Select.OptionText className="text-xs">{getEstimatedScheduleImpactLabel(option)}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
