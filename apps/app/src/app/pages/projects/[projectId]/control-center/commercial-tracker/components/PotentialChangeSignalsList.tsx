import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema, PotentialChangeSchema } from '@shape-construction/api/src';
import { Button } from '@shape-construction/arch-ui';
import { DocumentArrowDownIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { renderMarkup } from 'react-render-markup';
import { canLinkChangeSignals, canUnlinkChangeSignals } from '../utils/potentialChangeActions';
import { PotentialChangeSignalItem } from './PotentialChangeSignalItem';

type PotentialChangeSignalsListProps = {
  potentialChange: PotentialChangeSchema;
  onLinkChangeSignalsClick: () => void;
  onUnlinkChangeSignalClick: (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => void;
  isUnlinkingChangeSignals: boolean;
};

export function PotentialChangeSignalsList({
  potentialChange,
  onLinkChangeSignalsClick,
  onUnlinkChangeSignalClick,
  isUnlinkingChangeSignals,
}: PotentialChangeSignalsListProps) {
  const messages = useMessageGetter('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer');

  const linksForMessage = messages('changeSignalsList.linksFor', { title: potentialChange.title });
  const noLinksMessage = messages('emptyState.noLinks', { title: potentialChange.title });

  const hasLinks = !!potentialChange.signalsCount;

  const linkSignalsButton = canLinkChangeSignals(potentialChange) ? (
    <Button
      fullWidth={hasLinks}
      aria-label={messages('linkSignals')}
      size="xs"
      color="primary"
      variant="outlined"
      leadingIcon={PlusCircleIcon}
      onClick={onLinkChangeSignalsClick}
    >
      {messages('linkSignals')}
    </Button>
  ) : null;

  return (
    <div className="px-4 flex flex-col gap-4 grow">
      <div className="flex items-center h-12">
        <span className="text-sm leading-5 font-normal text-neutral-bold">
          {hasLinks
            ? renderMarkup(linksForMessage, { allowed: ['b'] })
            : renderMarkup(noLinksMessage, { allowed: ['b'] })}
        </span>
      </div>

      <div className="flex flex-col gap-4 grow">
        {hasLinks ? (
          <ul className="flex flex-col gap-2" aria-label={messages('changeSignalsList.changeSignals')}>
            {potentialChange.changeSignals.map((signal) => (
              <li key={signal.signalId}>
                {
                  <PotentialChangeSignalItem
                    signal={signal}
                    onUnlinkChangeSignalClick={onUnlinkChangeSignalClick}
                    isUnlinkingChangeSignals={isUnlinkingChangeSignals}
                    canUnlink={canUnlinkChangeSignals(potentialChange)}
                  />
                }
              </li>
            ))}
            {linkSignalsButton}
          </ul>
        ) : (
          <div className="flex flex-col grow items-center justify-center gap-6">
            <div className="flex flex-col items-center justify-center gap-2">
              <DocumentArrowDownIcon className="h-10 w-10 text-icon-neutral-subtle" />
              <p className="text-base leading-6 font-medium text-neutral-bold">{messages('emptyState.title')}</p>
              <p className="text-sm leading-5 font-normal text-neutral-subtle">{messages('emptyState.subtitle')}</p>
            </div>
            {linkSignalsButton}
          </div>
        )}
      </div>
    </div>
  );
}
