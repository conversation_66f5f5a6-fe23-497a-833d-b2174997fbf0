import { changeSignalsIssuesFactory, potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import {
  getApiProjectsPotentialChangeDetailsMockHandler,
  getApiProjectsProjectIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler,
  getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler,
  postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler,
  postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler,
} from '@shape-construction/api/handlers-factories/projects/control-center';
import { server } from 'tests/mock-server';
import { findAsyncToaster, render, screen, userEvent, waitFor } from 'tests/test-utils';
import { PotentialChangeSignalsDrawer } from './PotentialChangeSignalsDrawer';

describe('<PotentialChangeSignalsDrawer />', () => {
  it('renders the component with correct drawer title', async () => {
    const potentialChange = potentialChangeFactory();
    const setSelectedPotentialChange = jest.fn();
    const onClose = jest.fn();
    server.use(
      getApiProjectsProjectIdMockHandler(),
      getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
      getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler()
    );

    render(
      <PotentialChangeSignalsDrawer
        isOpen
        onClose={onClose}
        selectedPotentialChange={potentialChange}
        setSelectedPotentialChange={setSelectedPotentialChange}
      />
    );

    await waitFor(() => {
      expect(
        screen.getByRole('heading', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
        })
      ).toBeInTheDocument();
    });
  });

  describe('when the change signal is successfully linked', () => {
    it('renders back the list view', async () => {
      const potentialChange = potentialChangeFactory();
      const setSelectedPotentialChange = jest.fn();
      const onClose = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler()
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={onClose}
          selectedPotentialChange={potentialChange}
          setSelectedPotentialChange={setSelectedPotentialChange}
        />
      );

      await userEvent.click(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
        })
      );

      await waitFor(() => {
        expect(
          screen.getByRole('heading', {
            name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
          })
        ).toBeInTheDocument();
      });
    });

    it('show success toast', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler()
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
        />,
        { renderToast: true }
      );

      const startLinkingButton = await screen.findByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
      });
      await userEvent.click(startLinkingButton);
      const checkboxes = screen.getAllByRole('checkbox');
      await userEvent.click(checkboxes[0]);
      const linkButton = screen.getByRole('button', { name: 'controlCenter.changeSignals.actions.link' });
      await userEvent.click(linkButton);

      expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.linking.success')).toBeInTheDocument();
    });
  });

  describe('when the change signal linking fails', () => {
    it('show error toast', async () => {
      server.use(
        getApiProjectsProjectIdMockHandler(),
        getApiProjectsPotentialChangeDetailsMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
        getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMockHandler(
          undefined,
          {
            status: 500,
          }
        )
      );
      render(
        <PotentialChangeSignalsDrawer
          isOpen
          onClose={jest.fn()}
          selectedPotentialChange={potentialChangeFactory()}
          setSelectedPotentialChange={jest.fn()}
        />,
        { renderToast: true }
      );

      const startLinkingButton = await screen.findByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
      });
      await userEvent.click(startLinkingButton);
      const checkboxes = screen.getAllByRole('checkbox');
      await userEvent.click(checkboxes[0]);
      const linkButton = screen.getByRole('button', { name: 'controlCenter.changeSignals.actions.link' });
      await userEvent.click(linkButton);

      expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.linking.failed')).toBeInTheDocument();
    });
  });

  describe('when the change signal is unlinked', () => {
    describe('when the unlinking succeeds', () => {
      it('shows a successful unlinking toast', async () => {
        const changeSignal = changeSignalsIssuesFactory();
        const potentialChange = potentialChangeFactory({ changeSignals: [changeSignal], signalsCount: 1 });
        const setSelectedPotentialChange = jest.fn();
        const onClose = jest.fn();
        server.use(
          getApiProjectsProjectIdMockHandler(),
          getApiProjectsPotentialChangeDetailsMockHandler(() => potentialChange),
          getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
          getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
          postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler()
        );
        render(
          <PotentialChangeSignalsDrawer
            isOpen
            onClose={onClose}
            selectedPotentialChange={potentialChange}
            setSelectedPotentialChange={setSelectedPotentialChange}
          />,
          { renderToast: true }
        );

        await userEvent.click(
          await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink')
        );
        await userEvent.click(
          await screen.findByRole('button', { name: 'controlCenter.commercialTracker.modals.unlink.unlinkCTA' })
        );

        expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.unlinking.success')).toBeInTheDocument();
      });
    });
    describe('when the unlinking fails', () => {
      it('shows an error toast', async () => {
        const changeSignal = changeSignalsIssuesFactory();
        const potentialChange = potentialChangeFactory({ changeSignals: [changeSignal], signalsCount: 1 });
        const setSelectedPotentialChange = jest.fn();
        const onClose = jest.fn();
        server.use(
          getApiProjectsProjectIdMockHandler(),
          getApiProjectsPotentialChangeDetailsMockHandler(() => potentialChange),
          getApiProjectsProjectIdControlCenterChangeSignalIssuesMockHandler(),
          getApiProjectsProjectIdControlCenterChangeSignalDowntimesMockHandler(),
          postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler(
            undefined,
            { status: 500 }
          )
        );
        render(
          <PotentialChangeSignalsDrawer
            isOpen
            onClose={onClose}
            selectedPotentialChange={potentialChange}
            setSelectedPotentialChange={setSelectedPotentialChange}
          />,
          { renderToast: true }
        );

        await userEvent.click(
          await screen.findByLabelText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink')
        );
        await userEvent.click(
          await screen.findByRole('button', { name: 'controlCenter.commercialTracker.modals.unlink.unlinkCTA' })
        );

        expect(await findAsyncToaster('controlCenter.changeSignals.linkStatus.unlinking.failed')).toBeInTheDocument();
      });
    });
  });
});
