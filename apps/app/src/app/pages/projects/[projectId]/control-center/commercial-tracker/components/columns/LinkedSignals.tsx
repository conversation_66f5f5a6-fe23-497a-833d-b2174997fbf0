import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { Badge } from '@shape-construction/arch-ui';
import { SHAPE, THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import React from 'react';

type LinkedSignalsProps = {
  record: PotentialChangeSchema;
};

export const LinkedSignals: React.FC<LinkedSignalsProps> = ({ record }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  return (
    <button
      aria-label={messages('linkSignals')}
      type="button"
      className="text-xs leading-5 font-normal text-neutral-subtlest rounded-sm px-2 bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
    >
      {record.signalsCount ? (
        <Badge shape={SHAPE.BASIC} theme={THEME.BLUE} label={record.signalsCount.toString()} />
      ) : (
        messages('linkSignals')
      )}
    </button>
  );
};
