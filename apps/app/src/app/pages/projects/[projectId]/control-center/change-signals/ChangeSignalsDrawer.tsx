import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema } from '@shape-construction/api/src';
import { Drawer, IconButton } from '@shape-construction/arch-ui';
import { ArrowsMaximizeIcon, ArrowsMinimizeIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { useCurrentProject } from 'app/contexts/currentProject';
import {
  getChangeSignalsDowntimeInfiniteQueryOptions,
  getChangeSignalsInfiniteQueryOptions,
} from 'app/queries/control-center/change-signals';
import React, { useCallback, useState } from 'react';
import { ChangeSignalFilters } from './components/ChangeSignalFilters';
import { ChangeSignalsTable } from './components/ChangeSignalsTable';
import { SelectedChangeSignalsTable } from './components/SelectedChangeSignalsTable';

interface ChangeSignalsDrawerProps {
  isOpened?: boolean;
  onClose: () => void;
  onOpenLinkMode: () => void;
  selectedChangeSignals: ChangeSignalSchema[];
  setSelectedChangeSignals: (signals: ChangeSignalSchema[]) => void;
}

export const ChangeSignalsDrawer: React.FC<ChangeSignalsDrawerProps> = ({
  isOpened,
  onClose,
  onOpenLinkMode,
  setSelectedChangeSignals,
  selectedChangeSignals,
}: ChangeSignalsDrawerProps) => {
  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter.changeSignals');
  const [fullsceen, setFullscreen] = useState(false);
  const [selectedSignalType, setSelectedSignalType] = useState<ChangeSignalSchema['signalType']>('issue');

  const issuesQuery = useSuspenseInfiniteQuery(getChangeSignalsInfiniteQueryOptions(project.id));
  const downtimeQuery = useSuspenseInfiniteQuery(getChangeSignalsDowntimeInfiniteQueryOptions(project.id));
  const currentQuery = selectedSignalType === 'issue' ? issuesQuery : downtimeQuery;

  const currentChangeSignals = currentQuery.data.signals ?? [];
  const issuesCount = issuesQuery.data.total;
  const downtimeCount = downtimeQuery.data.total;

  const handleSelectionChange = useCallback((signals: ChangeSignalSchema[]) => {
    setSelectedChangeSignals(signals);
  }, []);

  const handleLoadMore = useCallback(() => {
    if (currentQuery.hasNextPage && !currentQuery.isFetchingNextPage) {
      currentQuery.fetchNextPage();
    }
  }, [currentQuery]);

  const onCloseDrawer = () => {
    setSelectedSignalType('issue');
    setFullscreen(false);
    onClose();
  };

  const onLinkMode = () => {
    onOpenLinkMode();
    onCloseDrawer();
  };

  const onRemoveSignals = (signalIds: ChangeSignalSchema['signalId'][]) =>
    setSelectedChangeSignals(selectedChangeSignals.filter(({ signalId }) => !signalIds.includes(signalId)));

  const toggleFullscreen = () => {
    setFullscreen(!fullsceen);
  };

  const toggleFullscreenButton = (
    <IconButton
      color="secondary"
      shape="square"
      size="xxs"
      variant="text"
      icon={fullsceen ? ArrowsMinimizeIcon : ArrowsMaximizeIcon}
      onClick={toggleFullscreen}
      tabIndex={-1}
    />
  );

  return (
    <Drawer.Root open={isOpened} onClose={onCloseDrawer} maxWidth={fullsceen ? 'none' : '3xl'}>
      <Drawer.Header className="p-4" onClose={onCloseDrawer} rightContent={toggleFullscreenButton}>
        <Drawer.Title>{messages('title')}</Drawer.Title>
      </Drawer.Header>
      <Drawer.Content className="overflow-x-hidden">
        <SelectedChangeSignalsTable
          changeSignals={selectedChangeSignals}
          onRemoveSignals={onRemoveSignals}
          onLinkSelection={onLinkMode}
        />
        <ChangeSignalFilters
          selectedType={selectedSignalType}
          onSignalTypeChange={setSelectedSignalType}
          issuesCount={issuesCount}
          downtimeCount={downtimeCount}
        />
        <ChangeSignalsTable
          changeSignals={currentChangeSignals}
          selectedSignals={selectedChangeSignals}
          onSelectionChange={handleSelectionChange}
          onLoadMore={handleLoadMore}
          hasNextPage={currentQuery.hasNextPage}
          isFetchingNextPage={currentQuery.isFetchingNextPage}
        />
      </Drawer.Content>
    </Drawer.Root>
  );
};
