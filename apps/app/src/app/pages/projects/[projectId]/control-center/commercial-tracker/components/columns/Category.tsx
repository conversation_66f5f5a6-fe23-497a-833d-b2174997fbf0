import { useMessageGetter } from '@messageformat/react';
import { potentialChangeCategoryEnum } from '@shape-construction/api/src';
import type {
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeCategoryEnumSchema,
  PotentialChangeSchema,
} from '@shape-construction/api/src';
import { Select } from '@shape-construction/arch-ui';
import { useFieldOptions } from '../../hooks/useFieldOptions';

type CategoryProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Category: React.FC<CategoryProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { getCategoryLabel } = useFieldOptions();

  return (
    <Select.Root
      onChange={(value: PotentialChangeCategoryEnumSchema) => {
        if (value !== record.category) {
          onUpdatePotentialChangeRecord({ category: value });
        }
      }}
      value={record.category}
      className="border-none w-40"
    >
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.category')}
        variant="plain"
        showChevronOnHover
      >
        {!record.category ? (
          <span className="text-xs leading-4 font-normal text-neutral-subtlest">{messages('select')}</span>
        ) : (
          <span className="text-xs leading-4 font-normal text-neutral-bold">{getCategoryLabel(record.category)}</span>
        )}
      </Select.Trigger>

      <Select.ResponsivePanel className="border-none">
        <Select.Options>
          {Object.values(potentialChangeCategoryEnum).map((category) => (
            <Select.Option key={category} value={category}>
              <Select.OptionText className="text-xs">{getCategoryLabel(category)}</Select.OptionText>
            </Select.Option>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
