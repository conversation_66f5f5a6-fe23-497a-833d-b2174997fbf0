import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema } from '@shape-construction/api/src';
import { Button, Table } from '@shape-construction/arch-ui';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { XCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDate } from '@shape-construction/utils/DateTime';
import { Link } from 'app/components/UI/Link/Link';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectLocations } from 'app/queries/projects/locations';
import { useProjectPeople } from 'app/queries/projects/people';
import { getSignalLink } from './ChangeSignalsTable';

type SelectedChangeSignalProps = {
  changeSignals: ChangeSignalSchema[];
  onRemoveSignals: (signalIds: ChangeSignalSchema['signalId'][]) => void;
  onLinkSelection: () => void;
};

function NoSelectedSignal() {
  const messages = useMessageGetter('controlCenter.changeSignals');
  return (
    <Table.Row className="h-32 text-center justify-center">
      <Table.Cell colSpan={5}>
        <div className="flex flex-col gap-2">
          <h2 className="text-base leading-6 font-medium text-neutral-bold">{messages('emptySelectedState.title')}</h2>
          <span className="text-neutral-subtle">{messages('emptySelectedState.subtitle')}</span>
        </div>
      </Table.Cell>
    </Table.Row>
  );
}

export const SelectedChangeSignalsTable: React.FC<SelectedChangeSignalProps> = ({
  changeSignals,
  onRemoveSignals,
  onLinkSelection,
}) => {
  const project = useCurrentProject();
  const { data: projectPeople } = useProjectPeople(project.id);
  const messages = useMessageGetter('controlCenter.changeSignals');
  const { data: locations } = useProjectLocations(project.id);

  const selectedCount = changeSignals?.length ?? 0;
  const disabledActions = selectedCount === 0;

  return (
    <Table.Container
      aria-label={messages('selectedChangeSignals')}
      className="flex flex-col md:rounded-none ring-0 min-h-48"
    >
      <div className="flex px-6 py-2 items-center justify-between bg-neutral-subtle border-b">
        <span className="text-xs leading-4 font-normal">{messages('selectionCount', { count: selectedCount })}</span>
        <div className="flex gap-2">
          <Button
            aria-label={messages('actions.clearSelection')}
            color="secondary"
            variant="outlined"
            size="xs"
            onClick={() => onRemoveSignals(changeSignals.map((signal) => signal.signalId))}
            disabled={disabledActions}
          >
            {messages('actions.clearSelection')}
          </Button>
          <Button
            aria-label={messages('actions.link')}
            color="primary"
            variant="contained"
            size="xs"
            onClick={onLinkSelection}
            disabled={disabledActions}
          >
            {messages('actions.link')}
          </Button>
        </div>
      </div>
      <div className="h-48 overflow-auto">
        <Table rowDensity="condensed" className="border-separate border-spacing-0">
          <Table.Heading className="sticky top-0 z-10">
            <Table.Row className="bg-neutral-subtlest">
              <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
                <span className="py-1">{messages('fields.title')}</span>
              </Table.Header>
              <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
                <span className="py-1">{messages('fields.author')}</span>
              </Table.Header>
              <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
                <span className="py-1">{messages('fields.date')}</span>
              </Table.Header>
              <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle">
                <span className="py-1">{messages('fields.location')}</span>
              </Table.Header>
              <Table.Header className="normal-case border-b py-2 first:sm:pl-8 bg-neutral-subtle" />
            </Table.Row>
          </Table.Heading>
          <Table.Body>
            {!changeSignals?.length && <NoSelectedSignal />}
            {changeSignals?.map((signal) => {
              const { signalId, title, locationId, publishedAt, teamMemberId } = signal;
              const teamMember = projectPeople?.find((person) => person.id === teamMemberId);
              return (
                <Table.Row striped key={signalId} className="border-b hover:bg-neutral-subtlest-hovered">
                  <Table.Cell className="border-b">
                    <Link
                      className="text-xs leading-5 font-medium text-link-brand cursor-pointer underline"
                      to={getSignalLink(signal, project.id)}
                    >
                      {title}
                    </Link>
                  </Table.Cell>
                  <Table.Cell className="border-b">
                    <div className="flex gap-2 items-center" aria-label={messages('fields.author')}>
                      <UserAvatar user={teamMember?.user} size="xs" />
                      <span className="text-neutral-bold text-xs leading-5 font-normal">{teamMember?.user.name}</span>
                    </div>
                  </Table.Cell>
                  <Table.Cell className="border-b">
                    <span className="text-neutral-bold text-xs leading-5 font-normal">
                      {formatDate(publishedAt, project.timezone, 'DD-MMM-YYYY')}
                    </span>
                  </Table.Cell>
                  <Table.Cell className="border-b">
                    <span className="text-neutral-bold text-xs leading-5 font-normal">
                      {truncatedLocationPath(locations, locationId)}
                    </span>
                  </Table.Cell>
                  <Table.Cell className="border-b">
                    <button
                      type="button"
                      onClick={() => onRemoveSignals([signalId])}
                      aria-label={messages('actions.removeSelection')}
                    >
                      <XCircleIcon className="h-4 w-4 text-icon-neutral" />
                    </button>
                  </Table.Cell>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </div>
    </Table.Container>
  );
};
