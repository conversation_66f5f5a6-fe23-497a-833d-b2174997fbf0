import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen } from 'tests/test-utils';
import { LinkedSignals } from './LinkedSignals';

describe('<LinkedSignals />', () => {
  it('renders a badge with the count of change signals', () => {
    const potentialChange = potentialChangeFactory({ signalsCount: 10 });
    render(<LinkedSignals record={potentialChange} />);

    expect(screen.getByText('10')).toBeInTheDocument();
  });

  describe('when there are no linked change signals', () => {
    it('renders a button to link change signals', () => {
      const potentialChange = potentialChangeFactory({ signalsCount: 0 });
      render(<LinkedSignals record={potentialChange} />);

      expect(screen.getByText('controlCenter.commercialTracker.linkSignals')).toBeInTheDocument();
    });
  });
});
