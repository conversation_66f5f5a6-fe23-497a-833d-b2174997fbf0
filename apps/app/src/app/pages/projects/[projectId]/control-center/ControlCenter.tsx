import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema, PotentialChangeSchema } from '@shape-construction/api/src';
import { <PERSON><PERSON>, Page } from '@shape-construction/arch-ui';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useModal } from '@shape-construction/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { useLinkChangeSignals } from 'app/queries/control-center/change-signals';
import {
  DEFAULT_VALUES,
  getPotentialChangesInfiniteQueryOptions,
  useCreatePotentialChange,
} from 'app/queries/control-center/commercial-tracker';
import { Suspense, useEffect, useRef, useState } from 'react';
import { ChangeSignalsDrawer } from './change-signals/ChangeSignalsDrawer';
import { CancelLinkPotentialChangesDialog } from './change-signals/components/CancelLinkPotentialChangesDialog';
import { LinkPotentialChangesAlert } from './change-signals/components/LinkPotentialChangesAlert';
import { CommercialTracker } from './commercial-tracker/CommercialTracker';

export const ControlCenter = () => {
  const messages = useMessageGetter('controlCenter');
  const { setLayoutConfig } = useLayoutContext();
  useEffect(() => {
    setLayoutConfig({ ...LayoutConfigs.initialVariant });
  }, [setLayoutConfig]);

  const project = useCurrentProject();
  const titleRef = useRef<HTMLInputElement>(null);
  const [newRecordId, setNewRecordId] = useState<PotentialChangeSchema['id']>();
  const {
    open: isChangeSignalDrawerOpened,
    openModal: openChangeSignalDrawer,
    closeModal: closeChangeSignalDrawer,
  } = useModal(false);

  const {
    open: isCancelLinkConfirmationOpen,
    openModal: openCancelLinkConfirmationOpen,
    closeModal: closeCancelLinkConfirmationOpen,
  } = useModal(false);

  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedPotentialChangeIds, setSelectedPotentialChangeIds] = useState<PotentialChangeSchema['id'][]>();
  const [selectedChangeSignals, setSelectedChangeSignals] = useState<ChangeSignalSchema[]>([]);
  const { mutateAsync: linkChangeSignals, isPending: isLinkingChangeSignals } = useLinkChangeSignals();
  const { mutateAsync: createNewPotentialChange, isPending: isNewChangePending } = useCreatePotentialChange();

  const queryClient = useQueryClient();

  const onOpenLinkMode = () => {
    setIsSelectionMode(true);
  };

  const onLinkPotentialChanges = async () => {
    try {
      if (!selectedPotentialChangeIds?.length || !selectedChangeSignals?.length) return;
      const changeSignalsPayload = selectedChangeSignals.map((signal) => ({
        change_signal_type: signal.signalType,
        change_signal_id: signal.signalId,
      }));
      const linkPromises = selectedPotentialChangeIds?.map((potentialChangeId) =>
        linkChangeSignals({
          projectId: project.id,
          potentialChangeId: potentialChangeId,
          data: {
            change_signals: changeSignalsPayload,
          },
        })
      );

      await Promise.all(linkPromises);
      await queryClient.invalidateQueries(getPotentialChangesInfiniteQueryOptions(project.id));
      setIsSelectionMode(false);
      setSelectedChangeSignals([]);
      setSelectedPotentialChangeIds([]);
      showSuccessToast({ message: messages('changeSignals.linkStatus.linking.success') });
    } catch {
      showErrorToast({ message: messages('changeSignals.linkStatus.linking.failed') });
    }
  };

  const onBackToSignals = () => {
    openChangeSignalDrawer();
    closeCancelLinkConfirmationOpen();
    setIsSelectionMode(false);
  };

  const onCancelProcess = () => {
    setIsSelectionMode(false);
    setSelectedPotentialChangeIds([]);
    setSelectedChangeSignals([]);
    closeCancelLinkConfirmationOpen();
  };

  const onCreatePotentialChange = async () => {
    const newChange = await createNewPotentialChange({ projectId: project.id, data: DEFAULT_VALUES });
    setNewRecordId(newChange.id);
    requestAnimationFrame(() => titleRef.current?.focus());
  };

  const canAddPotentialChange = project.availableActions.createPotentialChange;

  const addChangeButton = canAddPotentialChange ? (
    <Button
      aria-label={messages('commercialTracker.newPotentialChange')}
      color="primary"
      size="sm"
      variant="contained"
      onClick={onCreatePotentialChange}
      disabled={isNewChangePending || isSelectionMode}
      leadingIcon={PlusIcon}
    >
      {messages('commercialTracker.newPotentialChange')}
    </Button>
  ) : null;

  return (
    <Page>
      <Page.Header title={messages('pageTitle')} rightSection={addChangeButton} />
      <Suspense
        fallback={
          <Page.Body>
            <LoadingSpinner variant="screen" />
          </Page.Body>
        }
      >
        <Page.Body className="flex flex-col p-0 md:p-0 bg-neutral-white">
          {isSelectionMode && (
            <LinkPotentialChangesAlert
              onExitSelectionMode={openCancelLinkConfirmationOpen}
              onLinkPotentialChanges={onLinkPotentialChanges}
              isLinkDisabled={isLinkingChangeSignals || !selectedPotentialChangeIds?.length}
            />
          )}
          <CommercialTracker
            project={project}
            onOpenDrawer={openChangeSignalDrawer}
            isSelectionMode={isSelectionMode}
            selectedPotentialChangeIds={selectedPotentialChangeIds}
            setSelectedPotentialChangeIds={setSelectedPotentialChangeIds}
            titleRef={titleRef}
            onCreatePotentialChange={onCreatePotentialChange}
            newRecordId={newRecordId}
          />
          <ChangeSignalsDrawer
            isOpened={isChangeSignalDrawerOpened}
            onClose={closeChangeSignalDrawer}
            onOpenLinkMode={onOpenLinkMode}
            setSelectedChangeSignals={setSelectedChangeSignals}
            selectedChangeSignals={selectedChangeSignals}
          />
          <CancelLinkPotentialChangesDialog
            isOpen={isCancelLinkConfirmationOpen}
            close={closeCancelLinkConfirmationOpen}
            onCancelProcess={onCancelProcess}
            onBackToSignals={onBackToSignals}
          />
        </Page.Body>
      </Suspense>
    </Page>
  );
};

export { ControlCenter as Component };
