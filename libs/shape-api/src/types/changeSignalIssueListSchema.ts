/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ChangeSignalIssueDetailsBasicSchema } from './changeSignalIssueDetailsBasicSchema';
import type { CursorPaginationMetaSchema } from './cursorPaginationMetaSchema';

export type ChangeSignalIssueListSchema = {
  /**
   * @type array
   */
  entries: ChangeSignalIssueDetailsBasicSchema[];
  /**
   * @type object
   */
  meta: CursorPaginationMetaSchema;
};
