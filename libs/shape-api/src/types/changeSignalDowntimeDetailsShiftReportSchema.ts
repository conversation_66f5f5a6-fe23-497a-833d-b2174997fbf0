/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type ChangeSignalDowntimeDetailsShiftReportSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type string
   */
  contractorName: string | null;
  /**
   * @type string, date-time
   */
  publishedAt: string | null;
  /**
   * @type string, date
   */
  reportDate: string;
  /**
   * @type string
   */
  reportTitle: string | null;
  /**
   * @type string
   */
  shiftType: string | null;
  /**
   * @type string, uuid
   */
  teamId: string;
  /**
   * @type integer
   */
  teamMemberId: number;
};
