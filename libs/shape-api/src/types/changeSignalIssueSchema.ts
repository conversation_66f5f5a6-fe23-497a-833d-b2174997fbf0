/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ChangeSignalDetailsDocumentsSchema } from './changeSignalDetailsDocumentsSchema';
import type { ChangeSignalIssueDetailsBasicSchema } from './changeSignalIssueDetailsBasicSchema';
import type { IssueDetailsBasicSchema } from './issueDetailsBasicSchema';

export type ChangeSignalIssueSchema = ChangeSignalIssueDetailsBasicSchema &
  ChangeSignalDetailsDocumentsSchema & {
    /**
     * @type object
     */
    issue: IssueDetailsBasicSchema;
  };
