/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const changeSignalIssueDetailsBasicImpactEnum = {
  completedDelay: 'completedDelay',
  liveDelay: 'liveDelay',
  potentialDelay: 'potentialDelay',
} as const;

export type ChangeSignalIssueDetailsBasicImpactEnumSchema =
  (typeof changeSignalIssueDetailsBasicImpactEnum)[keyof typeof changeSignalIssueDetailsBasicImpactEnum];

export const changeSignalIssueDetailsBasicSignalTypeEnum = {
  issue: 'issue',
} as const;

export type ChangeSignalIssueDetailsBasicSignalTypeEnumSchema =
  (typeof changeSignalIssueDetailsBasicSignalTypeEnum)[keyof typeof changeSignalIssueDetailsBasicSignalTypeEnum];

export type ChangeSignalIssueDetailsBasicSchema = {
  impact: ChangeSignalIssueDetailsBasicImpactEnumSchema | null;
  /**
   * @type string, uuid
   */
  locationId: string | null;
  /**
   * @type string, date-time
   */
  publishedAt: string;
  /**
   * @type string, uuid
   */
  signalId: string;
  /**
   * @type string
   */
  signalType: ChangeSignalIssueDetailsBasicSignalTypeEnumSchema;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  title: string | null;
};
