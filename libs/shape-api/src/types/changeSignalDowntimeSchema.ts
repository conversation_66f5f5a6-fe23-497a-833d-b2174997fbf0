/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ChangeSignalDetailsDocumentsSchema } from './changeSignalDetailsDocumentsSchema';
import type { ChangeSignalDowntimeDetailsBasicSchema } from './changeSignalDowntimeDetailsBasicSchema';
import type { ChangeSignalDowntimeDetailsIssueSchema } from './changeSignalDowntimeDetailsIssueSchema';
import type { ChangeSignalDowntimeDetailsShiftReportSchema } from './changeSignalDowntimeDetailsShiftReportSchema';
import type { ShiftReportDownTimeDetailsBasicSchema } from './shiftReportDownTimeDetailsBasicSchema';

export type ChangeSignalDowntimeSchema = ChangeSignalDowntimeDetailsBasicSchema &
  ChangeSignalDetailsDocumentsSchema & {
    /**
     * @type object
     */
    shiftReport: ChangeSignalDowntimeDetailsShiftReportSchema;
  } & {
    issue: ChangeSignalDowntimeDetailsIssueSchema | null;
  } & {
    /**
     * @type object
     */
    downtime: ShiftReportDownTimeDetailsBasicSchema;
  };
