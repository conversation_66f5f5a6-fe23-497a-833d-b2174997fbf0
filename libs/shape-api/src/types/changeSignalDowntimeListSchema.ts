/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ChangeSignalDowntimeDetailsBasicSchema } from './changeSignalDowntimeDetailsBasicSchema';
import type { CursorPaginationMetaSchema } from './cursorPaginationMetaSchema';

export type ChangeSignalDowntimeListSchema = {
  /**
   * @type array
   */
  entries: ChangeSignalDowntimeDetailsBasicSchema[];
  /**
   * @type object
   */
  meta: CursorPaginationMetaSchema;
};
