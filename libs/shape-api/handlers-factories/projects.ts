import { http, HttpResponse, type HttpResponseInit, delay } from 'msw';
import { disciplineFactory } from '../factories/disciplines';
import { issueViewFactory } from '../factories/issueViews';
import { projectFactory } from '../factories/projects';
import type {
  DisciplineSchema,
  IssueViewListSchema,
  IssueViewSchema,
  PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationRequestSchema,
  PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSchema,
  PatchApiProjectsProjectIdMutationRequestSchema,
  PostApiProjectsMutationRequestSchema,
  PostApiProjectsProjectIdIssueViewsMutationRequestSchema,
  PostApiProjectsShowcasesMutationRequestSchema,
  PostApiProjectsShowcasesMutationResponseSchema,
  PotentialChangeSchema,
  ProjectListSchema,
  ProjectSchema,
} from '../src/types';
import type { Resolver } from './types';
import { queuedTaskProjectShowCaseFactory } from '../factories/exports';
import { potentialChangeFactory } from '../factories/control-center';

export const getApiProjectsMockHandler = (
  resolver?: Resolver<never, undefined, ProjectListSchema>,
  options?: HttpResponseInit
) => {
  return http.get<never, undefined, ProjectListSchema>('*/api/projects', async (info) => {
    const defaultData: ProjectListSchema = [];
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};

export const postApiProjectsMockHandler = (
  resolver?: Resolver<never, PostApiProjectsMutationRequestSchema, ProjectSchema>,
  options?: HttpResponseInit
) => {
  return http.post<never, PostApiProjectsMutationRequestSchema, ProjectSchema>('*/api/projects', async (info) => {
    const defaultData: ProjectSchema = projectFactory();
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};

export const getApiProjectsProjectIdMockHandler = (
  resolver?: Resolver<{ projectId: string }, undefined, ProjectSchema>,
  options?: HttpResponseInit
) => {
  return http.get<{ projectId: string }, undefined, ProjectSchema>('*/api/projects/:projectId', async (info) => {
    const defaultData: ProjectSchema = projectFactory({ id: info.params.projectId });
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};

export const patchApiProjectsProjectIdMockHandler = (
  resolver?: Resolver<{ projectId: string }, PatchApiProjectsProjectIdMutationRequestSchema, ProjectSchema>,
  options?: HttpResponseInit
) => {
  return http.patch<{ projectId: string }, PatchApiProjectsProjectIdMutationRequestSchema, ProjectSchema>(
    '*/api/projects/:projectId',
    async (info) => {
      const { project } = await info.request.clone().json();
      const defaultData: ProjectSchema = projectFactory({ id: info.params.projectId });
      const data = (await resolver?.(info)) ?? defaultData;

      return HttpResponse.json({ ...data, ...project }, options);
    }
  );
};

export const getApiProjectsIssueViewsMockHandler = (
  resolver?: Resolver<{ projectId: string }, undefined, IssueViewListSchema>,
  options?: HttpResponseInit
) => {
  return http.get<{ projectId: string }, undefined, IssueViewListSchema>(
    '*/api/projects/:projectId/issue_views',
    async (info) => {
      const defaultData: IssueViewListSchema = [];
      const data = (await resolver?.(info)) ?? defaultData;

      return HttpResponse.json(data, options);
    }
  );
};

export const getApiProjectsPotentialChangeDetailsMockHandler = (
  resolver?: Resolver<{ projectId: string }, undefined, PotentialChangeSchema>,
  options?: HttpResponseInit
) => {
  return http.get<{ projectId: string }, undefined, PotentialChangeSchema>(
    '*/api/projects/:project_id/control_center/potential_changes/:potential_change_id',
    async (info) => {
      const defaultData = potentialChangeFactory();
      const data = (await resolver?.(info)) ?? defaultData;

      return HttpResponse.json(data, options);
    }
  );
};

export const postApiProjectsIssueViewsMockHandler = (
  resolver?: Resolver<{ projectId: string }, PostApiProjectsProjectIdIssueViewsMutationRequestSchema, IssueViewSchema>,
  options?: HttpResponseInit
) => {
  return http.post<{ projectId: string }, PostApiProjectsProjectIdIssueViewsMutationRequestSchema, IssueViewSchema>(
    '*/api/projects/:projectId/issue_views',
    async (info) => {
      const defaultData: IssueViewSchema = issueViewFactory();
      const data = (await resolver?.(info)) ?? defaultData;

      return HttpResponse.json(data, options);
    }
  );
};

export const patchApiProjectsIssueViewsIssueViewIdMockHandler = (
  resolver?: Resolver<
    { projectId: string },
    PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSchema,
    IssueViewSchema
  >,
  options?: HttpResponseInit
) => {
  return http.patch<
    { projectId: string },
    PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSchema,
    IssueViewSchema
  >('*/api/projects/:projectId/issue_views/:issueViewId', async (info) => {
    const defaultData: IssueViewSchema = issueViewFactory();
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};

export const postApiProjectsProjectIdDefaultMockHandler = (
  resolver?: Resolver<{ projectId: string }, any, any>,
  options?: HttpResponseInit
) => {
  return http.post<{ projectId: string }, any, any>('*/api/projects/:projectId/default', async (info) => {
    await resolver?.(info);
    return HttpResponse.json(undefined, { status: 204, ...options });
  });
};

export const postApiProjectsProjectIdArchiveMockHandler = (
  resolver?: Resolver<{ projectId: string }, any, any>,
  options?: HttpResponseInit
) => {
  return http.post<{ projectId: string }, any, any>('*/api/projects/:projectId/archive', async (info) => {
    await resolver?.(info);
    return HttpResponse.json(undefined, { status: 204, ...options });
  });
};

export const postApiProjectsProjectIdDisciplinesSortMockHandler = (
  resolver?: Resolver<{ projectId: string }, any, any>,
  options?: HttpResponseInit
) => {
  return http.post<{ projectId: string }, any, any>('*/api/projects/:projectId/disciplines/sort', async (info) => {
    await resolver?.(info);
    return HttpResponse.json(undefined, { status: 204, ...options });
  });
};

export const patchApiProjectsProjectIdDisciplinesDisciplineIdMockHandler = (
  resolver?: Resolver<
    { projectId: string; disciplineId: string },
    PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationRequestSchema,
    DisciplineSchema
  >,
  options?: HttpResponseInit
) => {
  return http.patch<
    { projectId: string; disciplineId: string },
    PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationRequestSchema,
    DisciplineSchema
  >('*/api/projects/:projectId/disciplines/:disciplineId', async (info) => {
    const defaultData: DisciplineSchema = disciplineFactory();
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};

export const deleteApiProjectsProjectIdDisciplinesDisciplineIdMockHandler = (
  resolver?: Resolver<{ projectId: string; disciplineId: string }, undefined, any>,
  options?: HttpResponseInit
) => {
  return http.delete<{ projectId: string; disciplineId: string }, undefined, any>(
    '*/api/projects/:projectId/disciplines/:disciplineId',
    async (info) => {
      await delay(100);
      await resolver?.(info);
      return HttpResponse.json(undefined, options);
    }
  );
};

export const postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler = (
  resolver?: Resolver<{ projectId: string; messageId: string }, any, any>,
  options?: HttpResponseInit
) => {
  return http.post<{ projectId: string; messageId: string }, any, any>(
    '*/api/projects/:projectId/channels/messages/:messageId/save_attachments',
    async (info) => {
      await resolver?.(info);
      return HttpResponse.json(undefined, options);
    }
  );
};

export const postApiShowcaseProjectsMockHandler = (
  resolver?: Resolver<
    never,
    PostApiProjectsShowcasesMutationRequestSchema,
    PostApiProjectsShowcasesMutationResponseSchema
  >,
  options?: HttpResponseInit
) => {
  return http.post<
    never,
    PostApiProjectsShowcasesMutationRequestSchema,
    PostApiProjectsShowcasesMutationResponseSchema
  >('*/api/projects/showcases', async (info) => {
    const defaultData = queuedTaskProjectShowCaseFactory();
    const data = (await resolver?.(info)) ?? defaultData;

    return HttpResponse.json(data, options);
  });
};
